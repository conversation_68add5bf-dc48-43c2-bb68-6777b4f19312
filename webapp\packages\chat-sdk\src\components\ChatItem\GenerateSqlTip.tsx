import React, { ReactNode, useState } from 'react';
import { <PERSON><PERSON>, Too<PERSON><PERSON>, message } from 'antd';
import { CheckCircleFilled, CloseCircleFilled, CopyOutlined, DownOutlined, RightOutlined, SunOutlined, MoonOutlined } from '@ant-design/icons';
import { SqlInfoType } from '../../common/type';
import { PREFIX_CLS } from '../../common/constants';
import { format } from 'sql-formatter';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import classNames from 'classnames';

const prefixCls = `${PREFIX_CLS}-item`;

type Props = {
  sqlInfo: SqlInfoType;
  sqlTimeCost?: number;
  executeErrorMsg: string;
  isDeveloper?: boolean;
};

// 自定义浅色主题
const customLightTheme = {
  ...prism,
  'code[class*="language-"]': {
    ...prism['code[class*="language-"]'],
    color: '#333333',
  },
  'pre[class*="language-"]': {
    ...prism['pre[class*="language-"]'],
    color: '#333333',
  },
  '.token.keyword': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.function': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.operator': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.builtin': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.string': {
    color: '#22c55e',
  },
  '.token.comment': {
    color: '#6a9955',
    fontStyle: 'italic',
  },
  '.token.number': {
    color: '#d73a49',
  },
};

// 自定义深色主题
const customDarkTheme = {
  ...atomDark,
  'code[class*="language-"]': {
    ...atomDark['code[class*="language-"]'],
    color: '#ffffff',
  },
  'pre[class*="language-"]': {
    ...atomDark['pre[class*="language-"]'],
    color: '#ffffff',
  },
  '.token.keyword': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.function': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.operator': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.builtin': {
    color: '#0000FF',
    fontWeight: 'bold',
  },
  '.token.string': {
    color: '#22c55e',
  },
  '.token.comment': {
    color: '#6a9955',
    fontStyle: 'italic',
  },
  '.token.number': {
    color: '#b5cea8',
  },
};

const GenerateSqlTip: React.FC<Props> = ({
  sqlInfo,
  sqlTimeCost,
  executeErrorMsg,
  isDeveloper,
}) => {
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  const handleCopy = (_: string, result: any) => {
    result ? message.success('复制SQL成功', 1) : message.error('复制SQL失败', 1);
  };

  const handleThemeToggle = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  // 如果没有最终执行SQL，不显示组件
  if (!sqlInfo.querySQL) {
    return null;
  }

  const [collapsed, setCollapsed] = React.useState(true);

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
          </div>
          <span
            onClick={() => setCollapsed(!collapsed)}
            style={{ cursor: 'pointer', marginLeft: 11, color: 'var(--text-color-third)', fontSize: '12px' }}
          >
            {collapsed ? <RightOutlined /> : <DownOutlined />}
          </span>
        </div>
        {tipNode && !collapsed && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  // 如果有错误信息，显示失败状态
  if (executeErrorMsg) {
    return getNode(
      <>
        生成SQL失败
        {!!sqlTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {sqlTimeCost}ms)</span>
        )}
      </>,
      <div className={`${prefixCls}-sql-item`}>
        <div className={`${prefixCls}-sql-item-content`}>
          <div className={`${prefixCls}-sql-item-text`}>{executeErrorMsg}</div>
        </div>
      </div>,
      true
    );
  }

  const tipNode = (
    <div className={`${prefixCls}-tip`}>
      <div className={classNames(`${prefixCls}-sql-container`, isDarkTheme && `${prefixCls}-sql-container-dark`)}>
        {/* 标题栏，右侧放复制按钮和主题切换按钮 */}
        <div className={classNames(`${prefixCls}-sql-header`, isDarkTheme && `${prefixCls}-sql-header-dark`)}>
          <span className={`${prefixCls}-sql-title`}>SQL 查询语句</span>
          <div className={`${prefixCls}-sql-header-actions`}>
            <Tooltip title={isDarkTheme ? "切换到浅色主题" : "切换到深色主题"}>
              <Button
                type="text"
                icon={isDarkTheme ? <SunOutlined /> : <MoonOutlined />}
                className={classNames(`${prefixCls}-sql-theme-btn`, isDarkTheme && `${prefixCls}-sql-theme-btn-dark`)}
                onClick={handleThemeToggle}
              />
            </Tooltip>
            <CopyToClipboard
              text={format(sqlInfo.querySQL)}
              onCopy={(text, result) => handleCopy(text, result)}
            >
              <Tooltip title="复制代码">
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  className={classNames(`${prefixCls}-sql-copy-btn`, isDarkTheme && `${prefixCls}-sql-copy-btn-dark`)}
                />
              </Tooltip>
            </CopyToClipboard>
          </div>
        </div>
        {/* SQL内容区域 */}
        <div className={classNames(`${prefixCls}-sql-content`, isDarkTheme && `${prefixCls}-sql-content-dark`)}>
          <SyntaxHighlighter
            language="sql"
            style={isDarkTheme ? customDarkTheme : customLightTheme}
            showLineNumbers={true}
            customStyle={{
              margin: 0,
              padding: '16px',
              backgroundColor: isDarkTheme ? '#1F1F1F' : '#F9FAFB',
              border: 'none',
              borderRadius: 0,
              fontSize: '14px',
              lineHeight: '1.6',
            }}
          >
            {format(sqlInfo.querySQL)}
          </SyntaxHighlighter>
        </div>
      </div>
    </div>
  );

  return getNode(
    <>
      生成SQL
      {!!sqlTimeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {sqlTimeCost}ms)</span>
      )}
    </>,
    tipNode
  );
};

export default GenerateSqlTip;
